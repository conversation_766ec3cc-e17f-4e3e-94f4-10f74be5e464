#include <Arduino.h>
#include "TelemetryController.h"
#include "SystemController.h"
#include "SafetyController.h"

// Initialize the global telemetry controller
TelemetryController telemetry_controller;

TelemetryController::TelemetryController() 
  : rxState(WAIT_HEADER1)
  , rxIndex(0)
  , expectedPayloadLength(0) 
{}

void TelemetryController::processIncomingCommands(Stream &serialport) {
  while (serialport.available()) {
    uint8_t b = serialport.read();
    processByte(serialport, b);
  }
}

void TelemetryController::sendTelemetry(Stream &serialport) {
  // Recompute buffer size: 
  // 2 bytes (mode+fault)
  // + 1 byte sensorCount + CAL_SENSOR_COUNT * sizeof(float)
  // + 1 byte diCount + 1 byte digitalMask
  // + 1 byte actCount + ACTUATOR_COUNT * 1 byte
  uint8_t telemetryBuffer[
      1 + 1
    + 1 + CAL_SENSOR_COUNT * sizeof(float)
    + 1 + 1
    + 1 + ACTUATOR_COUNT-21
  ];
  uint16_t idx = 0;

  // 1) Mode + fault
  telemetryBuffer[idx++] = (uint8_t)system_controller.get_mode();
  telemetryBuffer[idx++] = (uint8_t)safety_controller.getFaultStatus();

  // 2) Sensor count + floats
  telemetryBuffer[idx++] = CAL_SENSOR_COUNT;
  for (uint8_t i = 0; i < CAL_SENSOR_COUNT; i++) {
    float v = getFilteredCalibratedValue(A0 + i, i);
    memcpy(&telemetryBuffer[idx], &v, sizeof(v));
    idx += sizeof(v);
  }

  // 3) Digital inputs: send count *and* a separate bitmask byte
  telemetryBuffer[idx++] = DIGITAL_INPUT_COUNT - 47;
  uint8_t digitalMask = 0;
  if (GetDigitalInputVal(PIN_ELEC_O2_DRYER_LEVEL) == HIGH) digitalMask |= 0x01;
  if (GetDigitalInputVal(PIN_ELEC_H2_DRYER_LEVEL) == HIGH) digitalMask |= 0x02;
  if (GetDigitalInputVal(PIN_FC_O2_DRYER_LEVEL) == HIGH) digitalMask |= 0x04;
  if (GetDigitalInputVal(PIN_SENSOR_FIRE_DETECT) == HIGH) digitalMask |= 0x08;
  if (GetDigitalInputVal(PIN_SENSOR_H2_AMBIENT) == HIGH) digitalMask |= 0x10;
  if (GetDigitalInputVal(PIN_EMERGENCY_STOP) == HIGH) digitalMask |= 0x20;  

  telemetryBuffer[idx++] = digitalMask;

  // 4) Actuator states array
  telemetryBuffer[idx++] = ACTUATOR_COUNT - 21;//One commented
  for (uint8_t i = 0; i < ACTUATOR_COUNT; i++) {
    telemetryBuffer[idx++] = (GetDigitalInputVal(21 + i) == HIGH) ? 1 : 0;
  }

  // 5) Frame & send with AA/55, length, cmd, CRC
  sendPacket(serialport, CMD_TELEMETRY_DATA, telemetryBuffer, idx);
}

void TelemetryController::sendAck(Stream &serialport, uint8_t origCommand, bool success, const char* message) {
  uint8_t payload[130];
  uint8_t msgLen = strlen(message);
  payload[0] = origCommand;
  payload[1] = success ? 1 : 0;
  memcpy(&payload[2], message, msgLen);
  sendPacket(serialport, CMD_ACKNOWLEDGMENT, payload, msgLen + 2);
}

void TelemetryController::sendLogMessage(Stream &serial, LogLevel level, const char* message) {
  const size_t maxChunk = 129; // 1 byte for level + up to 129 bytes of text = 130 payload
  size_t msgLen = strlen(message);
  size_t offset = 0;
  while (offset < msgLen) {
    size_t chunkLen = min(maxChunk, msgLen - offset);
    uint8_t payload[130];
    payload[0] = (uint8_t) level;
    memcpy(&payload[1], message + offset, chunkLen);
    //Serial1: DEBUG_SERIAL
    sendPacket(serial, CMD_LOG_MESSAGE, payload, chunkLen + 1);    
    offset += chunkLen;
  }
}

void TelemetryController::sendPacket(Stream &serialport, uint8_t command, const uint8_t* data, uint8_t dataLength) {
  uint8_t packet[256];
  uint8_t idx = 0;
  packet[idx++] = 0xAA;
  packet[idx++] = 0x55;
  packet[idx++] = dataLength;
  packet[idx++] = command;
  for (uint8_t i = 0; i < dataLength; i++) {
    packet[idx++] = data[i];
  }
  uint16_t crc = calculateCRC(&packet[2], dataLength + 2);
  packet[idx++] = highByte(crc);
  packet[idx++] = lowByte(crc);
  serialport.write(packet, idx);
}

void TelemetryController::processByte(Stream &serialport, uint8_t b) {
  switch (rxState) {
    case WAIT_HEADER1:
      if (b == 0xAA) rxState = WAIT_HEADER2;
      break;
    case WAIT_HEADER2:
      if (b == 0x55) rxState = WAIT_LENGTH;
      else rxState = WAIT_HEADER1;
      break;
    case WAIT_LENGTH:
      incomingDataLength = b;
      expectedPayloadLength = b + 3;
      rxIndex = 0;
      rxState = WAIT_PAYLOAD;
      break;
    case WAIT_PAYLOAD:
      if (rxIndex < sizeof(rxBuffer)) rxBuffer[rxIndex++] = b;
      if (rxIndex >= expectedPayloadLength) {
        if (expectedPayloadLength < 3) {
          sendAck(serialport, 0, false, "Invalid packet length");
          rxState = WAIT_HEADER1;
          break;
        }
        uint8_t dataLen = incomingDataLength;
        uint16_t receivedCrc = (rxBuffer[dataLen + 1] << 8) | rxBuffer[dataLen + 2];
        uint8_t crcBuffer[256];
        crcBuffer[0] = incomingDataLength;
        crcBuffer[1] = rxBuffer[0];
        for (uint8_t i = 0; i < dataLen; i++) {
          crcBuffer[2 + i] = rxBuffer[1 + i];
        }
        uint16_t calculatedCrc = calculateCRC(crcBuffer, dataLen + 2);
        if (calculatedCrc != receivedCrc) {
          sendAck(serialport, rxBuffer[0], false, "CRC error");
          rxState = WAIT_HEADER1;
          break;
        }
        ControlCommand cmd;
        cmd.command = rxBuffer[0];
        cmd.dataLength = dataLen;
        for (uint8_t i = 0; i < dataLen; i++) {
          cmd.data[i] = rxBuffer[1 + i];
        }
        processCommand(serialport, cmd);
        rxState = WAIT_HEADER1;
      }
      break;
  }
}

void TelemetryController::processCommand(Stream &serialport, const ControlCommand &cmd) {
  switch (cmd.command) {
    case CMD_MODE_CHANGE_REQUEST:
      if (cmd.dataLength < 1)
        sendAck(serialport, cmd.command, false, "Missing mode parameter");
      else {
        uint8_t newMode = cmd.data[0];
        system_controller.request_mode((SystemController::OperationMode)newMode);
        sendAck(serialport, cmd.command, true, "Mode change requested");
      }
      break;
    case CMD_CALIBRATION_GET:
      sendPacket(serialport, CMD_CALIBRATION_GET, (uint8_t*)&calibration_data, sizeof(CalibrationData));
      sendAck(serialport, cmd.command, true, "Calibration data sent");
      break;
    case CMD_CALIBRATION_UPDATE:
      if (cmd.dataLength != sizeof(CalibrationData))
        sendAck(serialport, cmd.command, false, "Invalid calibration data size");
      else {
        memcpy(&calibration_data, cmd.data, sizeof(CalibrationData));
        if (calibration_data.magic != CALIBRATION_MAGIC_VALUE)
          sendAck(serialport, cmd.command, false, "Invalid calibration magic");
        else {
          saveCalibrationToEEPROM();
          sendAck(serialport, cmd.command, true, "Calibration updated");
        }
      }
      break;
    case CMD_TELEMETRY_REQUEST:
      sendTelemetry(serialport);
      sendAck(serialport, cmd.command, true, "Telemetry sent");
      break;
    case CMD_SIMULATION_UPDATE:
      if (cmd.dataLength != SIM_UPDATE_PAYLOAD_LENGTH)
        sendAck(serialport, cmd.command, false, "Invalid simulation update data length");
      else {
        simulationMode = (cmd.data[0] != 0);
        memcpy(analogSensors, &cmd.data[1], sizeof(float) * NUM_ANALOG_SENSORS);
        memcpy(adsSensors, &cmd.data[1 + 64], sizeof(float) * 16);
        for (int i = 0; i < NUM_PZEMS; i++) {//EL,FC INV,INPUT
          int offset = 1 + 64 + 64 + i * 24;
          memcpy(&pzemModel[i], &cmd.data[offset], sizeof(PZEMModel));
        }
        memcpy(&simDigitalOutputs, &cmd.data[1 + 64 + 64 + 72], sizeof(simDigitalOutputs));
        sendAck(serialport, cmd.command, true, "Simulation update applied");
      }
      break;
    default:
      sendAck(serialport, cmd.command, false, "Unknown command");
      break;
  }
}
