#include <Arduino.h>
#include <EEPROM.h>
#include <CRC.h>
#include <jm_crc-ccitt.h>
#include <PID_v1.h>
#include <avr/wdt.h>
#include <avr/sleep.h>
#include <math.h>
#include <string.h>
#include <Wire.h>
#include <Adafruit_ADS1X15.h>
#include <PZEM004Tv30.h>

// Project headers
#include "Simulation.h"
#include "Constants.h"
#include "Sensors.h"
#include "Utilities.h"
#include "SafetyController.h"
#include "SystemController.h"
#include "ElectrolyzerController.h"
#include "FuelCellController.h"
#include "DryerController.h"
#include "TelemetryController.h"
// #include "BMSController.h"


// Global variables
const uint8_t ACTUATOR_PINS[ACTUATOR_COUNT-21] = {
  ACT_WATER_PUMP_RELAY, ACT_WATER_INLET_VALVE, ACT_ELEC_HEATER,
  ACT_H2_OUTPUT_VALVE, ACT_O2_OUTPUT_VALVE, ACT_H2_DRYER_DISCHARGE_VALVE,
  ACT_EL_O2_CHILLER_RELAY, ACT_ELEC_O2_DRYER_WATER_INLET_PUMP_RELAY,
  ACT_FC_H2_SUPPLY_VALVE, ACT_FC_O2_SUPPLY_VALVE, ACT_FC_O2_FAN,
  ACT_FC_H2_DISCHARGE_VALVE, ACT_FC_O2_CHILLER_RELAY, ACT_FC_O2_DRYER_PUMP,
  ACT_FC_LOAD_RELAY, ACT_BMS_CHARGE_RELAY, ACT_BMS_DISCHARGE_RELAY,
  ACT_INVERTER_RELAY, ACT_EMERGENCY_VENT, ACT_MAIN_INPUT_RELAY, ACT_MAIN_BMS_OUT_RELAY
};

const uint8_t DIGITAL_PINS[DIGITAL_INPUT_COUNT-47] = {
  PIN_ELEC_O2_DRYER_LEVEL, PIN_ELEC_H2_DRYER_LEVEL, PIN_FC_O2_DRYER_LEVEL,
  PIN_SENSOR_FIRE_DETECT, PIN_SENSOR_H2_AMBIENT, PIN_EMERGENCY_STOP
};

const char* actuatorNames[ACTUATOR_COUNT-21] = {
  "Water Pump", "Water Inlet Valve", "Electrical Heater",
  "H2 Output Valve", "O2 Output Valve", "H2 Dryer Discharge Valve",
  "O2 Dryer Discharge Valve", "Electrical O2 Chiller Relay",
  "Electrical O2 Dryer Water Inlet Pump", "FC H2 Supply Valve",
  "FC O2 Supply Valve", "FC O2 Fan", "FC H2 Discharge Valve",
  "FC O2 Chiller Relay", "FC O2 Dryer Pump", "FC Load Relay",
  "BMS Charge Relay", "BMS Discharge Relay", "Inverter Relay",
  "Emergency Vent", "Main Input Relay", "Main BMS Out Relay"
};

// Timing variables
unsigned long lastControlUpdateMs = 0;
unsigned long lastTelemetryMs     = 0;
unsigned long lastWatchdogResetMs = 0;

/**
 * System initialization
 * Sets up all hardware interfaces, controllers, and safety systems
 */
void setup() {
  // --- Debug serial (USB) ---
  DEBUG_SERIAL.begin(115200);
  while (!DEBUG_SERIAL && millis() < 3000) {
    // wait for native USB
  }

  // --- Telemetry serial (Hardware Serial1) ---
  TELEMETRY_SERIAL.begin(115200);
  // Optional: wait for telemetry link
  //while (!TELEMETRY_SERIAL) { }

  Serial.println(F("Elektrolizor System Starting..."));

  // Initialize I2C for sensor communication
  Wire.begin();

  // Initialize all digital output pins (actuators)
  for (uint8_t i = 21; i < ACTUATOR_COUNT - 21; i++) {
    pinMode(i, OUTPUT);
    digitalWrite(i, LOW);  // All actuators off initially
  }

  // Initialize all digital input pins with pull-ups
  pinMode(PIN_ELEC_O2_DRYER_LEVEL,  INPUT_PULLUP);
  pinMode(PIN_ELEC_H2_DRYER_LEVEL,  INPUT_PULLUP);
  pinMode(PIN_FC_O2_DRYER_LEVEL,    INPUT_PULLUP);
  pinMode(PIN_SENSOR_FIRE_DETECT,   INPUT_PULLUP);
  pinMode(PIN_SENSOR_H2_AMBIENT,    INPUT_PULLUP);
  pinMode(PIN_EMERGENCY_STOP,       INPUT_PULLUP);

  // Initialize simulation mode if enabled
  initializeSimulation();

  // Initialize sensor systems
  initializeSensors();

  // Initialize dryer controller
  dryer_controller.begin();

  // Initialize EL Regulator to 0V (safety)
  elRegulator.setOutputVoltage(0.0f);

  // Enable watchdog timer for system safety
  wdt_enable(WDTO_2S);

  // Log startup complete
  logMessage(LOG_INFO, F("System initialization complete."));
}

/**
 * Main system loop
 * Coordinates all controllers and handles timing of operations
 */
void loop() {
  unsigned long now = millis();

  // Reset watchdog timer periodically
  if (now - lastWatchdogResetMs >= 1000) {
    wdt_reset();
    lastWatchdogResetMs = now;
  }

  // --- Telemetry: process incoming commands over dedicated port ---
  telemetry_controller.processIncomingCommands(TELEMETRY_SERIAL);

  // --- Control loop ---
  // Update controllers at the specified control frequency
  if (now - lastControlUpdateMs >= Constants::CONTROL_LOOP_FREQ_MS) {
    lastControlUpdateMs = now;

    // Update sensor readings from ADS1115 modules
    updateADSReadings();

    // Update predictive buffers for trend analysis
    float waterTemp   = getFilteredCalibratedValueADS(ADC_SENSOR_WATER_TEMP, CAL_WATER_TEMP);
    float elecTemp    = getFilteredCalibratedValueADS(ADC_SENSOR_ELEC_TEMP, CAL_ELEC_TEMP);
    float bmsVoltage  = getFilteredCalibratedValueADS(ADC_SENSOR_BMS_VOLTAGE, CAL_BMS_VOLTAGE);

    waterTempBuffer.addSample(waterTemp);
    elecTempBuffer.addSample(elecTemp);
    bmsTempBuffer.addSample(bmsVoltage);

    // Update safety controller first (highest priority)
    safety_controller.update();
    system_controller.update();
    // bms_controller.update();

    // Update subsystem controllers based on current system mode
    switch (system_controller.get_mode()) {
      case SystemController::MODE_ELECTROLYZER:
        // Hydrogen production mode
        electrolyzer_controller.update();
        break;

      case SystemController::MODE_FUEL_CELL:
        // Power generation mode
        fuel_cell_controller.update();
        break;

      case SystemController::MODE_BATTERY_INVERTER:
        // Battery power mode			
        break;

      case SystemController::MODE_SYSTEM_TEST:
        // Test mode - handled by SystemController
        break;

      case SystemController::MODE_EMERGENCY:
        // Emergency mode - handled by SafetyController
        break;

      case SystemController::MODE_SAFE:
      default:
        // Safe mode - all actuators off
        break;
    }

    // Always update dryer controller (mode-independent)
    dryer_controller.update();
    
    // telemetry_controller.sendLogMessage(Serial1, LogLevel::LOG_INFO, "DEBUG Elektrolizor System Loop\n");
    // logMessage(LOG_INFO, F("Elektrolizor System Loop---\r\n"));
  }

  // --- Telemetry: send periodic data over dedicated port ---
  // Send telemetry data at the specified interval
  if (now - lastTelemetryMs >= Constants::TELEMETRY_INTERVAL_MS) {
    lastTelemetryMs = now;
    telemetry_controller.sendTelemetry(TELEMETRY_SERIAL);
    safety_controller.resetCommWatchdog();
  }

  // --- Emergency stop check ---
  // Check for emergency stop button (highest priority check)
  if (GetDigitalInputVal(PIN_EMERGENCY_STOP) == HIGH) {
    system_controller.force_mode(SystemController::MODE_EMERGENCY);
    safety_controller.emergency_shutdown();
    logMessage(LOG_ERROR, F("EMERGENCY STOP button pressed!"));
  }
}
