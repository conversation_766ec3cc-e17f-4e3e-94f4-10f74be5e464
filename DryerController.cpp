#include "DryerController.h"

// Initialize the global dryer controller
DryerController dryer_controller;

DryerController::DryerController()
  : sensorElO2(PIN_ELEC_O2_DRYER_LEVEL), 
    sensorElH2(PIN_ELEC_H2_DRYER_LEVEL), 
    sensorFcO2(PIN_FC_O2_DRYER_LEVEL),
    elO2DryerActive(false),
    elH2DryerActive(false),
    fcO2DryerActive(false),
    lastElO2DryerToggle(0),
    lastElH2DryerToggle(0),
    lastFcO2DryerToggle(0)
{}

void DryerController::begin() {
  pinMode(PIN_ELEC_O2_DRYER_LEVEL, INPUT_PULLUP);
  pinMode(PIN_ELEC_H2_DRYER_LEVEL, INPUT_PULLUP);
  pinMode(PIN_FC_O2_DRYER_LEVEL, INPUT_PULLUP);
}

void DryerController::update() {
  unsigned long now = millis();
  
  // Electrolyzer O2 Dryer
  if (GetDigitalInputVal(sensorElO2) == HIGH) {
    // Dryer level high (needs discharge)
    if (!elO2DryerActive && (now - lastElO2DryerToggle > DRYER_MIN_TOGGLE_INTERVAL)) {
      elO2DryerActive = true;
      SetDigitalOutputVal(ACT_ELEC_O2_DRYER_WATER_INLET_PUMP_RELAY, LOW);
      lastElO2DryerToggle = now;
      logMessage(LOG_INFO, F("DryerController: Electrolyzer O2 dryer discharge started."));
    }
  } else {
    // Dryer level low (stop discharge)
    if (elO2DryerActive && (now - lastElO2DryerToggle > DRYER_MIN_TOGGLE_INTERVAL)) {
      elO2DryerActive = false;
      SetDigitalOutputVal(ACT_ELEC_O2_DRYER_WATER_INLET_PUMP_RELAY, HIGH);
      lastElO2DryerToggle = now;
      logMessage(LOG_INFO, F("DryerController: Electrolyzer O2 dryer discharge stopped."));
    }
  }
  
  // Electrolyzer H2 Dryer
  if (GetDigitalInputVal(sensorElH2) == HIGH) {
    // Dryer level high (needs discharge)
    if (!elH2DryerActive && (now - lastElH2DryerToggle > DRYER_MIN_TOGGLE_INTERVAL)) {
      elH2DryerActive = true;
      SetDigitalOutputVal(ACT_H2_DRYER_DISCHARGE_VALVE, LOW);
      lastElH2DryerToggle = now;
      logMessage(LOG_INFO, F("DryerController: Electrolyzer H2 dryer discharge started."));
    }
  } else {
    // Dryer level low (stop discharge)
    if (elH2DryerActive && (now - lastElH2DryerToggle > DRYER_MIN_TOGGLE_INTERVAL)) {
      elH2DryerActive = false;
      SetDigitalOutputVal(ACT_H2_DRYER_DISCHARGE_VALVE, HIGH);
      lastElH2DryerToggle = now;
      logMessage(LOG_INFO, F("DryerController: Electrolyzer H2 dryer discharge stopped."));
    }
  }
  
  // Fuel Cell O2 Dryer
  if (GetDigitalInputVal(sensorFcO2) == HIGH) {
    // Dryer level high (needs discharge)
    if (!fcO2DryerActive && (now - lastFcO2DryerToggle > DRYER_MIN_TOGGLE_INTERVAL)) {
      fcO2DryerActive = true;
      SetDigitalOutputVal(ACT_FC_O2_DRYER_PUMP, HIGH);
      lastFcO2DryerToggle = now;
      logMessage(LOG_INFO, F("DryerController: Fuel Cell O2 dryer discharge started."));
    }
  } else {
    // Dryer level low (stop discharge)
    if (fcO2DryerActive && (now - lastFcO2DryerToggle > DRYER_MIN_TOGGLE_INTERVAL)) {
      fcO2DryerActive = false;
      SetDigitalOutputVal(ACT_FC_O2_DRYER_PUMP, LOW);
      lastFcO2DryerToggle = now;
      logMessage(LOG_INFO, F("DryerController: Fuel Cell O2 dryer discharge stopped."));
    }
  }
}
