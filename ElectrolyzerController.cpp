#include "ElectrolyzerController.h"

// Initialize the global electrolyzer controller
ElectrolyzerController electrolyzer_controller;

ElectrolyzerController::ElectrolyzerController()
  : lastPumpSwitchTime(0), lastHeaterSwitchTime(0),
    lastChillerSwitchTime(0), lastProductionSwitchTime(0), emergencyActive(false) {
  memset(&elecState, 0, sizeof(elecState));
}

void ElectrolyzerController::update() {
  if (emergencyActive) {
    // In emergency mode, skip normal operations.
    return;
  }
 
  updateADSReadings();
  elecState.elecTemp   = getFilteredCalibratedValueADS(ADC_SENSOR_ELEC_TEMP, CAL_ELEC_TEMP);
  elecState.waterTemp  = getFilteredCalibratedValueADS(ADC_SENSOR_WATER_TEMP, CAL_WATER_TEMP);
  elecState.waterPressure = getFilteredCalibratedValueADS(ADC_SENSOR_WATER_PRESS, CAL_WATER_PRESS);
  elecState.waterLevel = getFilteredCalibratedValueADS(ADC_SENSOR_WATER_LEVEL, CAL_WATER_LEVEL);
  elecState.elecVoltage= getVolt_EL();
  elecState.elecCurrent= getACSCurrent_EL();
 
  unsigned long now = millis();
 
  // Check water level
  if (elecState.waterLevel < WATER_LEVEL_MIN) {
    logMessage(LOG_ERROR, F("Electrolyzer: Insufficient water level!"));
    emergency_stop();
    return;
  }
 
  // Water pump (and inlet valve) control with debounce
  if (!elecState.isWaterPumpOn && (elecState.waterPressure < WATER_PRESSURE_START) &&
      (now - lastPumpSwitchTime >= ELECTROLYZER_MIN_SWITCH_INTERVAL)) {
    elecState.isWaterPumpOn = true;
    SetDigitalOutputVal(ACT_WATER_PUMP, HIGH);
    SetDigitalOutputVal(ACT_WATER_INLET_VALVE, HIGH);
    lastPumpSwitchTime = now;
    logMessage(LOG_INFO, F("Electrolyzer: Water pump activated."));
  }
  else if (elecState.isWaterPumpOn && (elecState.waterPressure > WATER_PRESSURE_STOP) &&
           (now - lastPumpSwitchTime >= ELECTROLYZER_MIN_SWITCH_INTERVAL)) {
    elecState.isWaterPumpOn = false;
    SetDigitalOutputVal(ACT_WATER_PUMP, LOW);
    SetDigitalOutputVal(ACT_WATER_INLET_VALVE, LOW);
    lastPumpSwitchTime = now;
    logMessage(LOG_INFO, F("Electrolyzer: Water pump deactivated."));
  }
 
  // Heater control with debounce
  if (!elecState.isHeaterOn && (elecState.waterTemp < WATER_TEMP_LOW) &&
      (now - lastHeaterSwitchTime >= ELECTROLYZER_MIN_SWITCH_INTERVAL)) {
    elecState.isHeaterOn = true;
    SetDigitalOutputVal(ACT_ELEC_HEATER, HIGH);
    lastHeaterSwitchTime = now;
    logMessage(LOG_INFO, F("Electrolyzer: Heater activated (water cold)."));
  }
  else if (elecState.isHeaterOn && (elecState.waterTemp >= WATER_TEMP_TARGET) &&
           (now - lastHeaterSwitchTime >= ELECTROLYZER_MIN_SWITCH_INTERVAL)) {
    elecState.isHeaterOn = false;
    SetDigitalOutputVal(ACT_ELEC_HEATER, LOW);
    lastHeaterSwitchTime = now;
    logMessage(LOG_INFO, F("Electrolyzer: Heater deactivated (water warmed)."));
  }
 
  // Chiller control with debounce
  if (!elecState.isChillerOn && (elecState.elecTemp > ELEC_TEMP_HIGH) &&
      (now - lastChillerSwitchTime >= ELECTROLYZER_MIN_SWITCH_INTERVAL)) {
    elecState.isChillerOn = true;
    SetDigitalOutputVal(ACT_EL_O2_CHILLER_RELAY, HIGH);
    lastChillerSwitchTime = now;
    logMessage(LOG_INFO, F("Electrolyzer: Chiller activated (elecTemp high)."));
  }
  else if (elecState.isChillerOn && (elecState.elecTemp < (ELEC_TEMP_HIGH - 5.0f)) &&
           (now - lastChillerSwitchTime >= ELECTROLYZER_MIN_SWITCH_INTERVAL)) {
    elecState.isChillerOn = false;
    SetDigitalOutputVal(ACT_EL_O2_CHILLER_RELAY, LOW);
    lastChillerSwitchTime = now;
    logMessage(LOG_INFO, F("Electrolyzer: Chiller deactivated (elecTemp normalized)."));
  }
 
  // Production control with debounce
  bool shouldProduce = (elecState.elecTemp >= PRODUCTION_START_TEMP) &&
                       (elecState.waterTemp >= WATER_TEMP_LOW + 5.0f) &&
                       (elecState.waterLevel >= WATER_LEVEL_MIN);
  if (!elecState.isProducing && shouldProduce &&
      (now - lastProductionSwitchTime >= ELECTROLYZER_MIN_SWITCH_INTERVAL)) {
    elecState.isProducing = true;
    SetDigitalOutputVal(ACT_H2_OUTPUT_VALVE, HIGH);
    SetDigitalOutputVal(ACT_O2_OUTPUT_VALVE, HIGH);
    elRegulator.setOutputVoltage(10.0f);  // Set regulator to 10V when production starts
    lastProductionSwitchTime = now;
    logMessage(LOG_INFO, F("Electrolyzer: Production started."));
  }
  else if (elecState.isProducing && ((!shouldProduce) || (elecState.elecTemp < PRODUCTION_STOP_TEMP)) &&
           (now - lastProductionSwitchTime >= ELECTROLYZER_MIN_SWITCH_INTERVAL)) {
    elecState.isProducing = false;
    SetDigitalOutputVal(ACT_H2_OUTPUT_VALVE, LOW);
    SetDigitalOutputVal(ACT_O2_OUTPUT_VALVE, LOW);
    elRegulator.setOutputVoltage(0.0f);  // Set regulator to 0V when production stops
    lastProductionSwitchTime = now;
    logMessage(LOG_INFO, F("Electrolyzer: Production stopped."));
  }
 
  // Emergency temperature check
  if (elecState.elecTemp > (ELEC_TEMP_HIGH + 10.0f) || elecState.waterTemp > WATER_TEMP_HIGH) {
    logMessage(LOG_ERROR, F("Electrolyzer: Temperature emergency!"));
    emergency_stop();
  }
}

void ElectrolyzerController::emergency_stop() {
  logMessage(LOG_ERROR, F("Electrolyzer: EMERGENCY STOP triggered."));
  SetDigitalOutputVal(ACT_ELEC_HEATER, LOW);
  SetDigitalOutputVal(ACT_H2_OUTPUT_VALVE, LOW);
  SetDigitalOutputVal(ACT_O2_OUTPUT_VALVE, LOW);
  SetDigitalOutputVal(ACT_WATER_PUMP, LOW);
  SetDigitalOutputVal(ACT_WATER_INLET_VALVE, LOW);
  SetDigitalOutputVal(ACT_EL_O2_CHILLER_RELAY, LOW);
  elRegulator.setOutputVoltage(0.0f);  // Set regulator to 0V during emergency stop
  elecState.isHeaterOn     = false;
  elecState.isProducing    = false;
  elecState.isWaterPumpOn  = false;
  elecState.isChillerOn    = false;
  emergencyActive    = true;
}

void ElectrolyzerController::resetEmergency() {
  emergencyActive = false;
  logMessage(LOG_INFO, F("Electrolyzer: Emergency reset."));
}
