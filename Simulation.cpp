#include "Simulation.h"

// Initialize simulation constants
namespace SimulationValues {
  const float WATER_LEVEL_RAW            = 50.0f / 100.0f;   // → 50 (Water Level)
  const float WATER_TEMP_RAW             = 25.0f / 80.0f;    // → 25°C (Water Temp)
  const float WATER_PRESS_RAW            = 3.0f  / 10.0f;    // → 3 (Water Pressure)
  const float ELEC_VOLTAGE               = 0.0f / 15.0f;    // → 0V (Electrolyzer Voltage)
  const float ELEC_CURRENT_RAW           = 0.0f / 55.0f;    // → 0A (Electrolyzer Current)
  const float ELECTROLIZOR_TEMP_RAW      = 30.0f / 80.0f;    // → 30°C (Electrolyzer Temp)
  const float ELECTROLIZOR_PRESS_RAW     = 3.0f  / 10.0f;    // → 3 (Electrolyzer Pressure)
  const float H2_TANK_TEMP_RAW           = 20.0f / 80.0f;    // → 20°C (H₂ Tank Temp)
  const float H2_TANK_PRESS_RAW          = 3.0f  / 10.0f;    // → 3 (H₂ Tank Pressure)
  const float O2_TANK_TEMP_RAW           = 20.0f / 80.0f;    // → 20°C (O₂ Tank Temp)
  const float O2_TANK_PRESS_RAW          = 3.0f  / 10.0f;    // → 3 (O₂ Tank Pressure)
  const float FC_VOLTAGE                 = 0.0f / 25.0f;    // → 0V (Fuel Cell Voltage)
  const float FC_CURRENT_RAW             = 0.0f / 20.0f;    // → 0A (Fuel Cell Current)
  const float BMS_VOLTAGE_RAW            = 24.0f / 80.0f;    // → 24V (BMS Voltage)
  const float BMS_CURRENT_RAW            = 0.0f / 50.0f;    // → 0A (BMS Current)
  const float H2_AMBIENT_RAW             = 0.0f;             // → 0% (H₂ Ambient)
  // const float WATER_LEVEL_RAW            = 50.0f / 100.0f;   // → 50 (Water Level)
  // const float WATER_TEMP_RAW             = 25.0f / 80.0f;    // → 25°C (Water Temp)
  // const float WATER_PRESS_RAW            = 3.0f  / 10.0f;    // → 3 (Water Pressure)
  // const float ELEC_VOLTAGE               = 0.0f / 0.0f;    // → 0V (Electrolyzer Voltage)
  // const float ELEC_CURRENT_RAW           = 0.0f / 0.0f;    // → 0A (Electrolyzer Current)
  // const float ELECTROLIZOR_TEMP_RAW      = 30.0f / 80.0f;    // → 30°C (Electrolyzer Temp)
  // const float ELECTROLIZOR_PRESS_RAW     = 3.0f  / 10.0f;    // → 3 (Electrolyzer Pressure)
  // const float H2_TANK_TEMP_RAW           = 20.0f / 80.0f;    // → 20°C (H₂ Tank Temp)
  // const float H2_TANK_PRESS_RAW          = 3.0f  / 10.0f;    // → 3 (H₂ Tank Pressure)
  // const float O2_TANK_TEMP_RAW           = 20.0f / 80.0f;    // → 20°C (O₂ Tank Temp)
  // const float O2_TANK_PRESS_RAW          = 3.0f  / 10.0f;    // → 3 (O₂ Tank Pressure)
  // const float FC_VOLTAGE                 = 0.0f / 0.0f;    // → 0V (Fuel Cell Voltage)
  // const float FC_CURRENT_RAW             = 0.0f / 0.0f;    // → 0A (Fuel Cell Current)
  // const float BMS_VOLTAGE_RAW            = 24.0f / 80.0f;    // → 24V (BMS Voltage)
  // const float BMS_CURRENT_RAW            = 0.0f / 0.0f;    // → 0A (BMS Current)
  // const float H2_AMBIENT_RAW             = 0.0f;             // → 0% (H₂ Ambient)
}

// Global simulation variables
bool simulationMode = SIM_MODE_INITIAL;
float analogSensors[NUM_ANALOG_SENSORS];
float adsSensors[15];
PZEMModel pzemModel[NUM_PZEMS];
DigitalInputSimulation simDigitalInputs;
bool simDigitalOutputs[ACTUATOR_COUNT-21];

float currentACS_EL_SimValue = 37;//Electrolyzer input current, sim value
float currentACS_FC_SimValue = 5;//Fuel Cell output current, sim value
float volt_EL_SimValue = 10;//Electrolyzer input Volt, sim value
float volt_FC_SimValue = 24;//Fuel Cell output Volt, sim value

// Initialize simulation values
void initializeSimulation() {
  // Initialize analog sensors with default values
  for (int i = 0; i < NUM_ANALOG_SENSORS; i++) {
    analogSensors[i] = 0.0f;
  }
  
  // Set specific analog sensor values (reordered to match new AnalogSensors enum)
  analogSensors[ADC_SENSOR_WATER_LEVEL] = SimulationValues::WATER_LEVEL_RAW;
  analogSensors[ADC_SENSOR_WATER_PRESS] = SimulationValues::WATER_PRESS_RAW;
  analogSensors[ADC_SENSOR_WATER_TEMP] = SimulationValues::WATER_TEMP_RAW;
  analogSensors[ADC_SENSOR_ELEC_TEMP] = SimulationValues::ELECTROLIZOR_TEMP_RAW;
  analogSensors[ADC_SENSOR_H2_TANK_PRESS] = SimulationValues::H2_TANK_PRESS_RAW;
  analogSensors[ADC_SENSOR_H2_TANK_TEMP] = SimulationValues::H2_TANK_TEMP_RAW;
  analogSensors[ADC_SENSOR_O2_TANK_PRESS] = SimulationValues::O2_TANK_PRESS_RAW;
  analogSensors[ADC_SENSOR_O2_TANK_TEMP] = SimulationValues::O2_TANK_TEMP_RAW;
  analogSensors[ADC_FC_VOLTAGE] = SimulationValues::FC_VOLTAGE;
  analogSensors[ADC_SENSOR_BMS_VOLTAGE] = SimulationValues::BMS_VOLTAGE_RAW;
  analogSensors[ADC_SENSOR_ELEC_VOLTAGE] = SimulationValues::ELEC_VOLTAGE;
  analogSensors[ADC_FC_CURRENT] = SimulationValues::FC_CURRENT_RAW;
  analogSensors[ADC_SENSOR_BMS_CURRENT] = SimulationValues::BMS_CURRENT_RAW;
  analogSensors[ADC_SENSOR_ELEC_CURRENT] = SimulationValues::ELEC_CURRENT_RAW;
  analogSensors[ADC_SENSOR_H2_AMBIENT] = SimulationValues::H2_AMBIENT_RAW;
  
  // Copy the same values to ADS sensors
  for (int i = 0; i < 15; i++) {
    adsSensors[i] = (i < NUM_ANALOG_SENSORS) ? analogSensors[i] : 0.0f;
  }
  
  // Initialize PZEM models
  for (int i = 0; i < NUM_PZEMS; i++) {
    pzemModel[i].voltage = 0.0f;
    pzemModel[i].current = 0.0f;
    pzemModel[i].power = 0.0f;
    pzemModel[i].energy = 0.0f;
    pzemModel[i].frequency = 50.0f;
    pzemModel[i].pf = 1.0f;
  }
  
  // Initialize digital inputs
  simDigitalInputs.elecO2DryerLevel = false;
  simDigitalInputs.elecH2DryerLevel = false;
  simDigitalInputs.fcO2DryerLevel = false;
  simDigitalInputs.sensorFireDetect = false;
  simDigitalInputs.sensorH2Ambient = false;
  simDigitalInputs.buttonEmergencyStop = false;
  
  // Initialize digital outputs
  for (int i = 0; i < ACTUATOR_COUNT-21; i++) {
    simDigitalOutputs[i] = false;
  }
}
