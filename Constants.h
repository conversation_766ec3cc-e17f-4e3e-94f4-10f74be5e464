#ifndef CONSTANTS_H
#define CONSTANTS_H

/**
 * Constants.h
 * Contains all system-wide constants, enums, and pin definitions
 * for the Elektrolizor project.
 */

#include <Arduino.h>

// Log levels for system messaging
enum LogLevel
{
  LOG_ERROR = 0,
  LOG_WARN,
  LOG_INFO,
  LOG_DEBUG
};
static const LogLevel currentLogLevel = LOG_INFO;

// Analog Sensor Indices
enum AnalogSensors
{
//LS1
//TT01
//PT1
//TT2
//PT2
//TT3
//PT3
//TT4

//VFC
//VBMS
//CFC
//CBMS
//CEL
//BOŞ
//BOŞ

  ADC_SENSOR_WATER_LEVEL = 0,//LS-01, ANALOG
  ADC_SENSOR_WATER_TEMP,//PT01
  ADC_SENSOR_WATER_PRESS,//TT01

  ADC_SENSOR_WATER_LEVEL = 0,//LS-01, ANALOG
  ADC_SENSOR_WATER_TEMP,//PT01
  ADC_SENSOR_WATER_PRESS,//TT01
  ADC_SENSOR_ELEC_VOLTAGE,// VEL
  ADC_SENSOR_ELEC_CURRENT,// CEL
  ADC_SENSOR_ELEC_TEMP,//TT-02
  ADC_SENSOR_ELEC_PRESS,--
  ADC_SENSOR_H2_TANK_TEMP,//TT-03
  ADC_SENSOR_H2_TANK_PRESS,//PT-02
  ADC_SENSOR_O2_TANK_TEMP,//TT-04
  ADC_SENSOR_O2_TANK_PRESS,//PT-04
  ADC_FC_VOLTAGE,// VFC
  ADC_FC_CURRENT,// CFC
  // ADC_SENSOR_BMS_TEMP,
  ADC_SENSOR_BMS_VOLTAGE,// VBMS
  ADC_SENSOR_BMS_CURRENT, // CBMS
  // DI_ELEC_O2_DRYER_LEVEL,//
  // DI_ELEC_H2_DRYER_LEVEL,//
  // DI_FC_O2_DRYER_LEVEL,//
  // DI_SENSOR_FIRE_DETECT,
  ADC_SENSOR_H2_AMBIENT // HS-01
};

// Digital Actuator Pins
enum DigitalActuator
{
  ACT_WATER_PUMP_RELAY = 22,//PM-02
  ACT_WATER_INLET_VALVE = 23,//SV-01
  ACT_ELEC_HEATER = 24,//EH-01
  ACT_H2_OUTPUT_VALVE = 25,//SV-03
  ACT_O2_OUTPUT_VALVE = 26,//SV-02
  ACT_H2_DRYER_DISCHARGE_VALVE = 27,//SV-04
  ACT_EL_O2_CHILLER_RELAY = 28,//CF-01
  ACT_ELEC_O2_DRYER_WATER_INLET_PUMP_RELAY = 29,//PM-01
  ACT_FC_H2_SUPPLY_VALVE = 30,//SV-05
  ACT_FC_O2_SUPPLY_VALVE = 31,//SV-06
  ACT_FC_O2_FAN = 32, // CF-06
  ACT_FC_H2_DISCHARGE_VALVE = 33,//SV-07
  ACT_FC_O2_CHILLER_RELAY = 34,//CF-02
  ACT_FC_O2_DRYER_PUMP = 35,//PM-03
  ACT_FC_LOAD_RELAY = 36,// R_FC_LOAD
  ACT_BMS_CHARGE_RELAY = 37, // R_BMS_CHARGE
  ACT_BMS_DISCHARGE_RELAY = 38, //R_BMS_DISCHARGE
  ACT_INVERTER_RELAY = 39, //R_INVERTER
  ACT_EMERGENCY_VENT = 40,     // SAFETY
  ACT_MAIN_INPUT_RELAY = 41,   //R_MAIN_INPUT & EL PSU // SAFETY
  ACT_MAIN_BMS_OUT_RELAY = 42, //R_BMS_OUT // SAFETY
  ACTUATOR_COUNT
};

// Digital Input Pins
enum DigitalInput
{
  PIN_ELEC_O2_DRYER_LEVEL = 47, // LS-02
  PIN_ELEC_H2_DRYER_LEVEL = 48, //LS-03
  PIN_FC_O2_DRYER_LEVEL = 49, //LS-04
  PIN_SENSOR_FIRE_DETECT = 50, //FS-01, SAFETY
  PIN_SENSOR_H2_AMBIENT = 51,  //HS-01, SAFETY
  PIN_EMERGENCY_STOP = 52,     //ES-01 SAFETY
  DIGITAL_INPUT_COUNT
};

// Sensor Calibration Indices
enum SensorCalIndex
{
  CAL_WATER_LEVEL = 0,
  CAL_WATER_TEMP,
  CAL_WATER_PRESS,
  CAL_ELEC_VOLTAGE,
  CAL_ELEC_CURRENT,
  CAL_ELEC_TEMP,
  CAL_ELEC_PRESS,
  CAL_H2_TANK_TEMP,
  CAL_H2_TANK_PRESS,
  CAL_O2_TANK_TEMP,
  CAL_O2_TANK_PRESS,
  CAL_FC_VOLTAGE,
  CAL_FC_CURRENT,
  // CAL_BMS_TEMP,
  CAL_BMS_VOLTAGE,
  CAL_BMS_CURRENT,
  // CAL_ELEC_O2_DRYER_LEVEL,
  // CAL_ELEC_H2_DRYER_LEVEL,
  // CAL_FC_O2_DRYER_LEVEL,
  // CAL_SENSOR_FIRE_DETECT,
  CAL_SENSOR_H2_AMBIENT,
  CAL_SENSOR_COUNT
};

// Telemetry Command Types
enum TelemetryCommandType
{
  CMD_MODE_CHANGE_REQUEST = 0x01,
  CMD_CALIBRATION_GET = 0x02,
  CMD_CALIBRATION_UPDATE = 0x03,
  CMD_TELEMETRY_DATA = 0x04,
  CMD_ACKNOWLEDGMENT = 0x05,
  CMD_LOG_MESSAGE = 0x06,
  CMD_TELEMETRY_REQUEST = 0x07,
  CMD_SIMULATION_UPDATE = 0x08
};

// ADS1115 Pins
#define ADS1_ALERT_PIN 43
#define ADS2_ALERT_PIN 44
#define ADS3_ALERT_PIN 45
#define ADS4_ALERT_PIN 46

// ELRegulator Pins
#define EL_REGULATOR_PWM_PIN 9        // PWM → PSU PWM input
#define EL_REGULATOR_VOLT_MON_PIN A2  // 0–5 V monitor (maps to 0–15 V)
#define EL_REGULATOR_CURR_MON_PIN A3  // 0–5 V monitor (maps to 0–55 A)

// ADS1115 Settings
#define ADS_GAIN GAIN_TWOTHIRDS
#define ADS_SPS RATE_ADS1115_250SPS
#define ADS_FULL_SCALE 6.144

// PZEM Settings
#define NUM_PZEMS 3

// #if !defined(PZEM_RX_PIN)
#define PZEM_RX_PIN 16
// #endif

// #if !defined(PZEM_TX_PIN)
#define PZEM_TX_PIN 17
// #endif

#if !defined(PZEM_SERIAL)
#define PZEM_SERIAL Serial2
#endif

// SERIAL SETTINGS
//  Aliases for clarity
#if !defined(DEBUG_SERIAL)
#define DEBUG_SERIAL Serial1 // USB serial for debug/logging
#endif

#if !defined(TELEMETRY_SERIAL)
#define TELEMETRY_SERIAL Serial // Hardware serial 1 for telemetry
#endif

// Simulation Mode
#define SIM_MODE_INITIAL true
#define NUM_ANALOG_SENSORS 16
#define SIM_UPDATE_PAYLOAD_LENGTH 205

// Predictive Buffer Size
#define PREDICTIVE_SAMPLES 30

/**
 * System Constants - Operational parameters for the system
 * Includes timing, safety thresholds, and operational limits
 */
namespace Constants
{
  // Timing constants (milliseconds)
  const unsigned long CONTROL_LOOP_FREQ_MS = 100;   // Main control loop frequency
  const unsigned long TELEMETRY_INTERVAL_MS = 1000; // Telemetry update interval
  const unsigned long MODE_CHANGE_DELAY_MS = 5000;  // Delay for mode transitions
  const unsigned long MAIN_LOOP_WATCHDOG_MS = 2000; // Watchdog timeout

  // Safety thresholds
  const float H2_LEAK_THRESHOLD_PERCENT = 4.0f; // H2 leak detection threshold
  const float MAX_OPERATING_TEMP_C = 80.0f;     // Maximum safe operating temperature
  const float MAX_TANK_PRESSURE_BAR = 5.0f;     // Maximum tank pressure

  // ELRegulator constants
  const float EL_REGULATOR_SUPPLY_MAX_V = 15.0f; // full-scale voltage
  const float EL_REGULATOR_SUPPLY_MAX_I = 55.0f; // full-scale current
  const float EL_REGULATOR_ADC_REF = 5.0f;       // Arduino analog reference (DEFAULT = Vcc)
  const int EL_REGULATOR_ADC_MAX = 1023;         // 10-bit max

  // Battery parameters
  const float BATT_MAX_VOLTAGE_V = 29.2f;      // Maximum battery voltage
  const float BATT_MIN_VOLTAGE_V = 20.0f;      // Minimum battery voltage
  const float BATT_MAX_TEMP_C = 60.0f;         // Maximum battery temperature
  const float LOW_BATTERY_THRESHOLD = 21.0f;   // Low battery warning threshold
  const float CHARGE_OFF_THRESHOLD = 28.0f;    // Stop charging threshold
  const float DISCHARGE_ON_THRESHOLD = 28.0f;  // Start discharge threshold
  const float DISCHARGE_OFF_THRESHOLD = 20.0f; // Stop discharge threshold
}

// Calibration Constants
static const uint32_t CALIBRATION_MAGIC_VALUE = 0xB1ADBEEF;
static const uint32_t CALIBRATION_VERSION = 0x10000001;

#endif // CONSTANTS_H
