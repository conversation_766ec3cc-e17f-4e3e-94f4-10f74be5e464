#include "SystemController.h"

// Initialize the global system controller
SystemController system_controller;

SystemController::SystemController()
  : currentMode(MODE_SAFE), pendingMode(MODE_SAFE),
    inTransition(false), transitionStart(0), subState(TRANS_IDLE), stepStartTime(0)
{}

void SystemController::request_mode(OperationMode m) {
  if (currentMode == MODE_EMERGENCY) {
    logMessage(LOG_WARN, F("SystemController: Already in EMERGENCY, ignoring mode change."));
    return;
  }

  if (validate_transition(m)) {
    logMessage(LOG_INFO, F("SystemController: Mode requested => "), String(m));
    begin_transition(m);
    transitionStart = millis();
  } else {
    logMessage(LOG_WARN, F("SystemController: Transition invalid => "), String(m));
  }
}

void SystemController::update() {
  if (inTransition) {
    if (millis() - transitionStart > (Constants::MODE_CHANGE_DELAY_MS * 2)) {
      logMessage(LOG_ERROR, F("SystemController: Transition timeout, reverting to SAFE mode."));
      force_mode(MODE_SAFE);
    } else {
      activateModeStep();
    }
  }
}

SystemController::OperationMode SystemController::get_mode() const {
  return currentMode;
}

void SystemController::force_mode(OperationMode m) {
  currentMode = m;
  deactivate_current_mode();
  pendingMode = m;
  inTransition = false;
  subState = TRANS_DONE;
  logMessage(LOG_INFO, F("SystemController: Force mode => "), String(m));
}

const __FlashStringHelper* SystemController::getModeName(OperationMode mode) {
  switch (mode) {
    case MODE_SAFE: return F("MODE_SAFE");
    case MODE_ELECTROLYZER: return F("MODE_ELECTROLYZER");
    case MODE_FUEL_CELL: return F("MODE_FUEL_CELL");
    case MODE_BATTERY_INVERTER: return F("MODE_BATTERY_INVERTER");
    case MODE_SYSTEM_TEST: return F("MODE_SYSTEM_TEST");
    case MODE_EMERGENCY: return F("MODE_EMERGENCY");
    default: return F("UNKNOWN");
  }
}

bool SystemController::validate_transition(OperationMode m) {
  float h2Ambient = getFilteredCalibratedValueADS(ADC_SENSOR_H2_AMBIENT, CAL_SENSOR_H2_AMBIENT);
  if (h2Ambient >= Constants::H2_LEAK_THRESHOLD_PERCENT) {
    logMessage(LOG_ERROR, F("SystemController: H₂ leak detected, cannot transition."));
    return false;
  }

  float h2TankPress = getFilteredCalibratedValueADS(ADC_SENSOR_H2_TANK_PRESS, CAL_H2_TANK_PRESS);
  if (h2TankPress > Constants::MAX_TANK_PRESSURE_BAR) {
    logMessage(LOG_ERROR, F("SystemController: H₂ tank overpressure."));
    return false;
  }

  // float bmsTemp = getFilteredCalibratedValueADS(ADC_SENSOR_BMS_TEMP, CAL_BMS_TEMP);
  // if (bmsTemp > Constants::BATT_MAX_TEMP_C) {
  //   logMessage(LOG_ERROR, F("SystemController: BMS temperature too high."));
  //   return false;
  // }

  if (m == MODE_ELECTROLYZER) {
    float waterLevel = getFilteredCalibratedValueADS(ADC_SENSOR_WATER_LEVEL, CAL_WATER_LEVEL);
    if (waterLevel < 2.0f) {
      logMessage(LOG_ERROR, F("SystemController: Water level too low for electrolyzer operation."));
      return false;
    }
  }

  return true;
}

void SystemController::begin_transition(OperationMode m) {
  deactivate_current_mode();
  pendingMode = m;
  inTransition = true;
  subState = TRANS_IDLE;
  logMessage(LOG_INFO, F("SystemController: Beginning transition to "), String(m));
}

void SystemController::complete_transition() {
  currentMode = pendingMode;
  inTransition = false;
  subState = TRANS_DONE;
  logMessage(LOG_INFO, F("SystemController: Transition complete => "), String(currentMode));
}

void SystemController::deactivate_current_mode() {
  logMessage(LOG_INFO, F("SystemController: Deactivating mode => "), String(currentMode));

  switch (currentMode) {
    case MODE_ELECTROLYZER:
      SetDigitalOutputVal(ACT_ELEC_HEATER, HIGH);
      SetDigitalOutputVal(ACT_H2_OUTPUT_VALVE, HIGH);
      SetDigitalOutputVal(ACT_O2_OUTPUT_VALVE, HIGH);
      SetDigitalOutputVal(ACT_WATER_PUMP_RELAY, HIGH);
      SetDigitalOutputVal(ACT_WATER_INLET_VALVE, HIGH);
      SetDigitalOutputVal(ACT_BMS_DISCHARGE_RELAY, HIGH);      
      elRegulator.setOutputVoltage(0.0f);  // Ensure regulator is at 0V for all mode deactivations 
      break;

    case MODE_FUEL_CELL:
      SetDigitalOutputVal(ACT_FC_LOAD_RELAY, HIGH);
      SetDigitalOutputVal(ACT_FC_H2_SUPPLY_VALVE, HIGH);
      SetDigitalOutputVal(ACT_FC_O2_SUPPLY_VALVE, HIGH);
      SetDigitalOutputVal(ACT_FC_O2_FAN, HIGH);
      SetDigitalOutputVal(ACT_BMS_DISCHARGE_RELAY, HIGH);
      SetDigitalOutputVal(ACT_INVERTER_RELAY, HIGH);
      SetDigitalOutputVal(ACT_FC_H2_DISCHARGE_VALVE, HIGH);
      SetDigitalOutputVal(ACT_FC_O2_CHILLER_RELAY, HIGH);
      SetDigitalOutputVal(ACT_FC_O2_DRYER_PUMP, HIGH);
      elRegulator.setOutputVoltage(0.0f);  // Ensure regulator is at 0V for all mode deactivations 
      break;

    case MODE_BATTERY_INVERTER:
      SetDigitalOutputVal(ACT_INVERTER_RELAY, HIGH);
      SetDigitalOutputVal(ACT_BMS_DISCHARGE_RELAY, HIGH);
      elRegulator.setOutputVoltage(0.0f);  // Ensure regulator is at 0V for all mode deactivations 
      break;

    default:
      for (uint8_t pin = 21; pin < 21 + ACTUATOR_COUNT; pin++) {
        SetDigitalOutputVal(pin, HIGH);
      }
      elRegulator.setOutputVoltage(0.0f);  // Ensure regulator is at 0V for all mode deactivations 
      break;
  }
}

void SystemController::activateModeStep() {
  switch (pendingMode) {
    case MODE_ELECTROLYZER: doActivateElectrolyzer(); break;
    case MODE_FUEL_CELL: doActivateFuelCell(); break;
    case MODE_BATTERY_INVERTER: doActivateBatteryInverter(); break;
    case MODE_SYSTEM_TEST: doSystemTest(); break;
    case MODE_EMERGENCY: doEmergency(); break;
    case MODE_SAFE:
    default:
      currentMode = pendingMode;
      subState = TRANS_DONE;
      break;
  }
}

void SystemController::doActivateElectrolyzer() {
  switch (subState) {
    case TRANS_IDLE:
      logMessage(LOG_INFO, F("SystemController: Activating Electrolyzer mode."));
      SetDigitalOutputVal(ACT_WATER_INLET_VALVE, LOW);
      SetDigitalOutputVal(ACT_WATER_PUMP_RELAY, LOW);
      stepStartTime = millis();
      subState = TRANS_WAIT;
      break;

    case TRANS_WAIT:
      if (millis() - stepStartTime > 500UL) {
        // float waterPress = getFilteredCalibratedValueADS(ADC_SENSOR_WATER_PRESS, CAL_WATER_PRESS);
        // if (waterPress < 3.0f) {
        //   logMessage(LOG_WARN, F("SystemController: Water pressure still low during electrolyzer activation."));
        //   elRegulator.setOutputVoltage(0.0f);  // Ensure regulator is at 0V
        // }
        SetDigitalOutputVal(ACT_ELEC_HEATER, LOW);
        subState = TRANS_VERIFY;
        stepStartTime = millis();
      }
      break;

    case TRANS_VERIFY: {
        float waterLevel = getFilteredCalibratedValueADS(ADC_SENSOR_WATER_LEVEL, CAL_WATER_LEVEL);
        if (waterLevel < 2.0f) {
          logMessage(LOG_ERROR, F("SystemController: Water level too low during electrolyzer activation."));          
          elRegulator.setOutputVoltage(0.0f);  // Ensure regulator is at 0V
          force_mode(MODE_SAFE);
          return;
        }
        elRegulator.setOutputVoltage(10.0f);  // Set regulator to 10V when electrolyzer mode is activated
      }
      subState = TRANS_DONE;
      complete_transition();
      break;

    case TRANS_DONE:
    default:
      currentMode = pendingMode;
      break;
  }
}

void SystemController::doActivateFuelCell() {
  switch (subState) {
    case TRANS_IDLE:
      logMessage(LOG_INFO, F("SystemController: Activating Fuel Cell mode."));
      SetDigitalOutputVal(ACT_FC_H2_SUPPLY_VALVE, HIGH);
      SetDigitalOutputVal(ACT_FC_O2_SUPPLY_VALVE, HIGH);
      SetDigitalOutputVal(ACT_FC_H2_DISCHARGE_VALVE, HIGH);
      stepStartTime = millis();
      subState = TRANS_WAIT;
      break;

    case TRANS_WAIT:
      if (millis() - stepStartTime > 1000UL) {
        SetDigitalOutputVal(ACT_FC_LOAD_RELAY, HIGH);
        SetDigitalOutputVal(ACT_INVERTER_RELAY, HIGH);
        SetDigitalOutputVal(ACT_FC_O2_CHILLER_RELAY, HIGH);
        subState = TRANS_VERIFY;
        stepStartTime = millis();
      }
      break;

    case TRANS_VERIFY: {
        float fcVoltage = getVolt_FC();
        if (fcVoltage < 15.0f || fcVoltage > 30.0f) {
          logMessage(LOG_ERROR, F("SystemController: Fuel Cell voltage abnormal during activation."));
          force_mode(MODE_SAFE);
          return;
        }
      }
      subState = TRANS_DONE;
      complete_transition();
      break;

    case TRANS_DONE:
    default:
      currentMode = pendingMode;
      break;
  }
}

void SystemController::doActivateBatteryInverter() {
  switch (subState) {
    case TRANS_IDLE:
      logMessage(LOG_INFO, F("SystemController: Activating Battery Inverter mode."));
      stepStartTime = millis();
      subState = TRANS_WAIT;
      break;

    case TRANS_WAIT:
      if (millis() - stepStartTime > 2000UL) {
        SetDigitalOutputVal(ACT_INVERTER_RELAY, HIGH);
        subState = TRANS_DONE;
      }
      break;

    case TRANS_DONE:
    default:
      currentMode = pendingMode;
      break;
  }
}

void SystemController::doSystemTest() {
  static uint8_t pinIndex = 0;
  static bool stateHigh = false;
  static unsigned long toggleMs = 0;

  switch (subState) {
    case TRANS_IDLE:
      logMessage(LOG_INFO, F("SystemController: Starting System Test."));
      pinIndex = 0;
      stateHigh = true;
      toggleMs = millis();
      subState = TRANS_WAIT;
      break;

    case TRANS_WAIT:
      if (millis() - toggleMs > 200UL) {
        uint8_t pin = 21 + pinIndex;
        SetDigitalOutputVal(pin, stateHigh ? HIGH : LOW);
        toggleMs = millis();
        stateHigh = !stateHigh;
        if (!stateHigh) {
          pinIndex++;
          if (pinIndex >= ACTUATOR_COUNT) {
            request_mode(MODE_SAFE);
            subState = TRANS_DONE;
          }
        }
      }
      break;

    case TRANS_DONE:
    default:
      currentMode = pendingMode;
      break;
  }
}

void SystemController::doEmergency() {
  logMessage(LOG_ERROR, F("SystemController: EMERGENCY mode triggered."));
  SetDigitalOutputVal(ACT_EMERGENCY_VENT, HIGH);
  elRegulator.setOutputVoltage(0.0f);  // Ensure regulator is at 0V during emergency
  deactivate_current_mode();
  currentMode = pendingMode;
  subState = TRANS_DONE;
}
