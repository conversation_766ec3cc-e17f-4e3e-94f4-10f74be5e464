#include <Arduino.h>
#include "FuelCellController.h"
#include "Constants.h"
#include "Sensors.h"

// Initialize the global fuel cell controller
FuelCellController fuel_cell_controller;

FuelCellController::FuelCellController()
  : pidSetpoint(OUTPUT_POWER_SETPOINT), pidInput(0.0), pidOutput(0.0),
    pid(&pidInput, &pidOutput, &pidSetpoint, 2.0, 5.0, 1.0, DIRECT)
{
  fcState.fcCurrent = 0.0f;
  fcState.fcVoltage = 0.0f;
  fcState.outputPower = 0.0f;
  fcState.isActive = false;
  // pid.SetMode(AUTOMATIC);
  // pid.SetOutputLimits(0, 255);
}

void FuelCellController::update() {
  fcState.fcVoltage = getVolt_FC();// getPZEMVoltage(1);
  fcState.fcCurrent = getACSCurrent_FC();//getPZEMCurrent(1);
  fcState.outputPower = fcState.fcVoltage * fcState.fcCurrent;// getPZEMPower(1);
  
  if (fcState.fcVoltage < MIN_OPERATING_VOLTAGE || fcState.fcVoltage > MAX_OPERATING_VOLTAGE) {
    logMessage(LOG_ERROR, F("FuelCell: Voltage out-of-range, shutting down."));
    char buffer[10];
    dtostrf(fcState.fcVoltage, 6, 3, buffer);
    logMessage(LOG_ERROR, F("Voltage:"), String(buffer));
    shutdown();
    return;
  }
  
  if (fcState.outputPower > MAX_OUTPUT_POWER) {
    logMessage(LOG_ERROR, F("FuelCell: Output power too high, shutting down."));
    shutdown();
    return;
  }
  
  if (fcState.isActive) {
    // pidInput = fcState.outputPower;
    // pid.Compute();
    // SetAnalogOut(ACT_FC_O2_FAN, (int)pidOutput);      
    SetDigitalOutputVal(ACT_FC_O2_FAN, HIGH);
    
    if (fcState.outputPower < MIN_OUTPUT_POWER) {
      logMessage(LOG_WARN, F("FuelCell: Output power below optimal level."));
    }
  } else {
    fcState.outputPower = 0.0f;
    // SetAnalogOut(ACT_FC_O2_FAN, 0);
    SetDigitalOutputVal(ACT_FC_O2_FAN, LOW);
  }
}

void FuelCellController::activate(bool enable) {
  fcState.isActive = enable;
  
  if (enable)
    logMessage(LOG_INFO, F("FuelCell: Activated."));
  else
    logMessage(LOG_WARN, F("FuelCell: Deactivated."));

  SetDigitalOutputVal(ACT_FC_H2_SUPPLY_VALVE, enable ? HIGH : LOW);
  SetDigitalOutputVal(ACT_FC_O2_SUPPLY_VALVE, enable ? HIGH : LOW);
  SetDigitalOutputVal(ACT_FC_LOAD_RELAY, enable ? HIGH : LOW);
  SetDigitalOutputVal(ACT_FC_H2_DISCHARGE_VALVE, enable ? HIGH : LOW);
  SetDigitalOutputVal(ACT_FC_O2_FAN, enable ? HIGH : LOW);     
  // if (!enable)
    // SetAnalogOut(ACT_FC_O2_FAN, 0);
}

void FuelCellController::shutdown() {
  logMessage(LOG_ERROR, F("FuelCell: Shutting down due to fault."));
  activate(false);
}
